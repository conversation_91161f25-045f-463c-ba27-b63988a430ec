@extends('layouts.app')

@section('title', 'Dashboard')

@section('content')
    <div class="welcome-header">
        <h1>Welcome to PawPortal!</h1>
        <p>Select a section from the left menu to manage your pets and services.</p>

        @if($totalPets > 0)
        <div class="quick-stats">
            <div class="stat-item">
                <span class="stat-number">{{ $totalPets }}</span>
                <span class="stat-label">Total Pets</span>
            </div>
            <div class="stat-item">
                <span class="stat-number">{{ $petsWithVaccinations }}</span>
                <span class="stat-label">Vaccinated</span>
            </div>
            <div class="stat-item">
                <span class="stat-number">{{ $recentVaccinations }}</span>
                <span class="stat-label">Recent Shots</span>
            </div>
        </div>
        @else
        <div class="empty-state">
            <p>🐾 You haven't added any pets yet. Start by adding your first pet's health record!</p>
            <a href="{{ route('pet_health.create') }}" class="cta-button">Add Your First Pet</a>
        </div>
        @endif
    </div>

    <!-- Analytics Section -->
    <div class="analytics-section">
        <h2>📊 Your Pet Analytics</h2>
        <div class="analytics-grid">
            <div class="analytics-card">
                <div class="circle-chart" data-percent="{{ $totalPets > 0 ? min(($petsWithVaccinations / $totalPets) * 100, 100) : 0 }}">
                    <div class="circle-chart-inner">
                        <span class="percentage">{{ $totalPets > 0 ? round(($petsWithVaccinations / $totalPets) * 100) : 0 }}%</span>
                        <span class="label">Vaccinated</span>
                    </div>
                </div>
                <h4>Vaccination Status</h4>
                <p>{{ $petsWithVaccinations }} of {{ $totalPets }} pets vaccinated</p>
            </div>

            <div class="analytics-card">
                <div class="circle-chart" data-percent="{{ $totalPets > 0 ? min(($petsWithMedicalHistory / $totalPets) * 100, 100) : 0 }}">
                    <div class="circle-chart-inner">
                        <span class="percentage">{{ $totalPets > 0 ? round(($petsWithMedicalHistory / $totalPets) * 100) : 0 }}%</span>
                        <span class="label">Records</span>
                    </div>
                </div>
                <h4>Medical Records</h4>
                <p>{{ $petsWithMedicalHistory }} of {{ $totalPets }} pets have medical history</p>
            </div>

            <div class="analytics-card">
                <div class="circle-chart" data-percent="{{ $totalPets > 0 ? min(($recentVaccinations / $totalPets) * 100, 100) : 0 }}">
                    <div class="circle-chart-inner">
                        <span class="percentage">{{ $totalPets > 0 ? round(($recentVaccinations / $totalPets) * 100) : 0 }}%</span>
                        <span class="label">Recent</span>
                    </div>
                </div>
                <h4>Recent Vaccinations</h4>
                <p>{{ $recentVaccinations }} pets vaccinated in last 6 months</p>
            </div>

            <div class="analytics-card">
                <div class="circle-chart" data-percent="{{ $totalPets }}">
                    <div class="circle-chart-inner">
                        <span class="percentage">{{ $totalPets }}</span>
                        <span class="label">Total</span>
                    </div>
                </div>
                <h4>Total Pets</h4>
                <p>You have {{ $totalPets }} {{ $totalPets == 1 ? 'pet' : 'pets' }} registered</p>
            </div>
        </div>

        <!-- Species Breakdown -->
        @if($totalPets > 0)
        <div class="species-breakdown">
            <h3>🐕 Pet Species Breakdown</h3>
            <div class="species-grid">
                @foreach($speciesBreakdown as $species => $count)
                <div class="species-item">
                    <div class="species-bar">
                        <div class="species-fill" style="width: {{ ($count / $totalPets) * 100 }}%"></div>
                    </div>
                    <span class="species-label">{{ $species }} ({{ $count }})</span>
                </div>
                @endforeach
            </div>
        </div>
        @endif
    </div>

    <div class="dashboard-cards">
        <div class="dashboard-card">
            <h3>🐾 Pet Health & Records</h3>
            <p>Manage your pets' health information, vaccination records, and medical history.</p>
            <a href="{{ route('pet_health.index') }}" class="card-link">View Records</a>
        </div>

        <div class="dashboard-card">
            <h3>🏠 Pet Adoption & Shelter</h3>
            <p>Find pets available for adoption or list pets that need new homes.</p>
            <a href="{{ route('pet_adoption') }}" class="card-link">Browse Pets</a>
        </div>

        <div class="dashboard-card">
            <h3>💬 Online Consultation</h3>
            <p>Connect with veterinarians for professional advice and consultations.</p>
            <a href="{{ route('online_consultation') }}" class="card-link">Start Consultation</a>
        </div>

        <div class="dashboard-card">
            <h3>🔍 Lost & Found Board</h3>
            <p>Report lost pets or help reunite found pets with their owners.</p>
            <a href="{{ route('lost_found') }}" class="card-link">View Board</a>
        </div>

        <div class="dashboard-card">
            <h3>📊 Multi-Pet Dashboard</h3>
            <p>Manage multiple pets and get an overview of all your pet-related activities.</p>
            <a href="{{ route('multi_pet') }}" class="card-link">View Dashboard</a>
        </div>
    </div>

    <style>
        .welcome-header {
            margin-bottom: 40px;
        }

        .welcome-header h1 {
            margin-bottom: 10px;
        }

        .quick-stats {
            display: flex;
            gap: 30px;
            margin-top: 25px;
            flex-wrap: wrap;
        }

        .stat-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 15px 25px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            color: white;
            min-width: 100px;
        }

        .stat-number {
            font-size: 32px;
            font-weight: bold;
            line-height: 1;
        }

        .stat-label {
            font-size: 12px;
            opacity: 0.9;
            margin-top: 5px;
        }

        .empty-state {
            text-align: center;
            padding: 40px;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            border-radius: 15px;
            color: white;
            margin-top: 25px;
        }

        .empty-state p {
            font-size: 18px;
            margin-bottom: 20px;
        }

        .cta-button {
            display: inline-block;
            background: white;
            color: #f5576c;
            padding: 12px 25px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: bold;
            transition: transform 0.3s ease;
        }

        .cta-button:hover {
            transform: translateY(-2px);
        }

        .analytics-section {
            margin-bottom: 40px;
        }

        .analytics-section h2 {
            color: #2c3e50;
            margin-bottom: 25px;
            font-size: 24px;
        }

        .analytics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 25px;
            margin-bottom: 40px;
        }

        .analytics-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            text-align: center;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .analytics-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.15);
        }

        .circle-chart {
            position: relative;
            width: 120px;
            height: 120px;
            margin: 0 auto 20px;
            border-radius: 50%;
            background: conic-gradient(
                #3498db 0deg,
                #3498db calc(var(--percent) * 3.6deg),
                #ecf0f1 calc(var(--percent) * 3.6deg),
                #ecf0f1 360deg
            );
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .circle-chart-inner {
            width: 90px;
            height: 90px;
            background: white;
            border-radius: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .percentage {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
            line-height: 1;
        }

        .label {
            font-size: 12px;
            color: #7f8c8d;
            margin-top: 2px;
        }

        .analytics-card h4 {
            margin: 0 0 10px 0;
            color: #2c3e50;
            font-size: 16px;
        }

        .analytics-card p {
            margin: 0;
            color: #7f8c8d;
            font-size: 14px;
        }

        .species-breakdown {
            background: white;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .species-breakdown h3 {
            margin: 0 0 20px 0;
            color: #2c3e50;
            font-size: 18px;
        }

        .species-grid {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .species-item {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .species-bar {
            flex: 1;
            height: 25px;
            background: #ecf0f1;
            border-radius: 12px;
            overflow: hidden;
            position: relative;
        }

        .species-fill {
            height: 100%;
            background: linear-gradient(90deg, #3498db, #2ecc71);
            border-radius: 12px;
            transition: width 1.5s ease-in-out;
            animation: fillAnimation 1.5s ease-in-out;
        }

        @keyframes fillAnimation {
            from { width: 0% !important; }
        }

        .species-label {
            min-width: 100px;
            font-size: 14px;
            color: #2c3e50;
            font-weight: 500;
        }

        .dashboard-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .dashboard-card {
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.15);
        }

        .dashboard-card h3 {
            margin: 0 0 15px 0;
            color: #2c3e50;
            font-size: 20px;
        }

        .dashboard-card p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .card-link {
            display: inline-block;
            background-color: #3498db;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 500;
            transition: background-color 0.3s ease;
        }

        .card-link:hover {
            background-color: #2980b9;
        }
    </style>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Animate circle charts
            const circles = document.querySelectorAll('.circle-chart');

            circles.forEach(circle => {
                const percent = circle.getAttribute('data-percent');
                const isTotal = circle.parentElement.querySelector('h4').textContent === 'Total Pets';

                if (isTotal) {
                    // For total pets, use a different color scheme
                    circle.style.background = `conic-gradient(
                        #e74c3c 0deg,
                        #e74c3c ${Math.min(percent * 10, 360)}deg,
                        #ecf0f1 ${Math.min(percent * 10, 360)}deg,
                        #ecf0f1 360deg
                    )`;
                } else {
                    // Animate the percentage
                    circle.style.setProperty('--percent', 0);

                    setTimeout(() => {
                        circle.style.transition = 'all 1.5s ease-in-out';
                        circle.style.setProperty('--percent', percent);
                    }, 200);
                }
            });

            // Add different colors for different charts
            const chartColors = ['#3498db', '#2ecc71', '#f39c12', '#e74c3c'];
            circles.forEach((circle, index) => {
                const percent = circle.getAttribute('data-percent');
                const color = chartColors[index % chartColors.length];

                setTimeout(() => {
                    circle.style.background = `conic-gradient(
                        ${color} 0deg,
                        ${color} calc(${percent} * 3.6deg),
                        #ecf0f1 calc(${percent} * 3.6deg),
                        #ecf0f1 360deg
                    )`;
                }, 300 + (index * 200));
            });
        });
    </script>
@endsection
