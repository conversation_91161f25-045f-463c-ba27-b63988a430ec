<?php $__env->startSection('title', 'Edit Pet Health Record'); ?>

<?php $__env->startSection('content'); ?>
    <h2 class="form-title">Edit Pet Health Record</h2>

    <?php if($errors->any()): ?>
        <div class="alert alert-danger">
            <ul>
                <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <li><?php echo e($error); ?></li>
                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
            </ul>
        </div>
    <?php endif; ?>

    <form method="POST" action="<?php echo e(route('pet_health.update', $petHealthRecord)); ?>" class="health-form">
        <?php echo csrf_field(); ?>
        <?php echo method_field('PUT'); ?>

        <div class="form-group">
            <label for="pet_name">Pet Name</label>
            <input type="text" name="pet_name" id="pet_name" value="<?php echo e(old('pet_name', $petHealthRecord->pet_name)); ?>" placeholder="e.g. Buddy" required>
        </div>

        <div class="form-group">
            <label for="species">Species</label>
            <input type="text" name="species" id="species" value="<?php echo e(old('species', $petHealthRecord->species)); ?>" placeholder="e.g. Dog" required>
        </div>

        <div class="form-group">
            <label for="breed">Breed</label>
            <input type="text" name="breed" id="breed" value="<?php echo e(old('breed', $petHealthRecord->breed)); ?>" placeholder="e.g. Golden Retriever">
        </div>

        <div class="form-group">
            <label for="medical_history">Medical History</label>
            <textarea name="medical_history" id="medical_history" rows="3" placeholder="e.g. Dewormed, Allergy to chicken"><?php echo e(old('medical_history', $petHealthRecord->medical_history)); ?></textarea>
        </div>

        <div class="form-group">
            <label for="last_vaccination">Last Vaccination Date</label>
            <input type="date" name="last_vaccination" id="last_vaccination" value="<?php echo e(old('last_vaccination', $petHealthRecord->last_vaccination)); ?>">
        </div>

        <button type="submit" class="submit-button">💾 Update Record</button>
        <a href="<?php echo e(route('pet_health.index')); ?>" class="back-button">← Back to Records</a>
    </form>

    <style>
        .form-title {
            font-size: 26px;
            color: #2c3e50;
            margin-bottom: 25px;
        }

        .alert-danger {
            background-color: #f8d7da;
            color: #721c24;
            padding: 12px;
            border-radius: 6px;
            margin-bottom: 20px;
            border: 1px solid #f5c6cb;
        }

        .alert-danger ul {
            margin: 0;
            padding-left: 20px;
        }

        .health-form {
            max-width: 500px;
            background: #ffffff;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.08);
        }

        .form-group {
            margin-bottom: 20px;
        }

        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #34495e;
        }

        input[type="text"],
        input[type="date"],
        textarea {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid #ccc;
            border-radius: 6px;
            font-size: 15px;
        }

        textarea {
            resize: vertical;
        }

        .submit-button {
            width: 100%;
            padding: 12px;
            background-color: #3498db;
            color: white;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .submit-button:hover {
            background-color: #2980b9;
        }

        .back-button {
            display: inline-block;
            margin-top: 15px;
            padding: 10px 15px;
            background-color: #95a5a6;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-size: 14px;
            transition: background-color 0.3s ease;
        }

        .back-button:hover {
            background-color: #7f8c8d;
        }
    </style>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\pawportal\resources\views/pet_health/edit.blade.php ENDPATH**/ ?>