<?php $__env->startSection('title', 'Dashboard'); ?>

<?php $__env->startSection('content'); ?>
    <h1>Welcome to PawPortal!</h1>
    <p>Select a section from the left menu to manage your pets and services.</p>

    <div class="dashboard-cards">
        <div class="dashboard-card">
            <h3>🐾 Pet Health & Records</h3>
            <p>Manage your pets' health information, vaccination records, and medical history.</p>
            <a href="<?php echo e(route('pet_health.index')); ?>" class="card-link">View Records</a>
        </div>

        <div class="dashboard-card">
            <h3>🏠 Pet Adoption & Shelter</h3>
            <p>Find pets available for adoption or list pets that need new homes.</p>
            <a href="<?php echo e(route('pet_adoption')); ?>" class="card-link">Browse Pets</a>
        </div>

        <div class="dashboard-card">
            <h3>💬 Online Consultation</h3>
            <p>Connect with veterinarians for professional advice and consultations.</p>
            <a href="<?php echo e(route('online_consultation')); ?>" class="card-link">Start Consultation</a>
        </div>

        <div class="dashboard-card">
            <h3>🔍 Lost & Found Board</h3>
            <p>Report lost pets or help reunite found pets with their owners.</p>
            <a href="<?php echo e(route('lost_found')); ?>" class="card-link">View Board</a>
        </div>

        <div class="dashboard-card">
            <h3>📊 Multi-Pet Dashboard</h3>
            <p>Manage multiple pets and get an overview of all your pet-related activities.</p>
            <a href="<?php echo e(route('multi_pet')); ?>" class="card-link">View Dashboard</a>
        </div>
    </div>

    <style>
        .dashboard-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }

        .dashboard-card {
            background: white;
            border-radius: 10px;
            padding: 25px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .dashboard-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.15);
        }

        .dashboard-card h3 {
            margin: 0 0 15px 0;
            color: #2c3e50;
            font-size: 20px;
        }

        .dashboard-card p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .card-link {
            display: inline-block;
            background-color: #3498db;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 500;
            transition: background-color 0.3s ease;
        }

        .card-link:hover {
            background-color: #2980b9;
        }
    </style>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\pawportal\resources\views/dashboard.blade.php ENDPATH**/ ?>